import { Action } from "@ngrx/store";

export enum AutoDialerActionTypes {
    FETCH_AUTO_DIALER = '[Auto Dialer] Fetch Auto Dialer',
    FETCH_AUTO_DIALER_SUCCESS = '[Auto Dialer] Fetch Auto Dialer Success',
    FETCH_STATUS_COUNT = '[Auto Dialer] Fetch Status Count',
    FETCH_STATUS_COUNT_SUCCESS = '[Auto Dialer] Fetch Status Count Success',
}

export class FetchAutoDialer implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_AUTO_DIALER;
    constructor() { }
}

export class FetchAutoDialerSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_AUTO_DIALER_SUCCESS;
    constructor(public response: any) { }
}

export class FetchStatusCount implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_STATUS_COUNT;
    constructor() { }
}

export class FetchStatusCountSuccess implements Action {
    readonly type: string = AutoDialerActionTypes.FETCH_STATUS_COUNT_SUCCESS;
    constructor(public response: any) { }
}
