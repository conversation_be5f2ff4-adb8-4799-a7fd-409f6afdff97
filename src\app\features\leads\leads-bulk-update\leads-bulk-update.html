<div class="d-flex scrollbar max-w-100-260 tb-max-w-100-190 ip-max-w-100-70 scroll-hide">
  <div class="flex-center">
    <div class="fw-600 text-white mr-20 text-xl text-nowrap">{{gridApi?.getSelectedNodes()?.length}}
      {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}</div>
  </div>
  <div class="flex-center">
    <button class="btn-bulk-1"
      *ngIf="(currentPath === '/invoice' ? canBulkUpdateStatusInvoice : canBulkUpdateStatus) && leadVisibilityEnum[appliedFilter?.leadVisibility]  !== 'Unassigned' && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted'"
      id="btnBulkUpdateStatus" data-automate-id="btnBulkUpdateStatus"
      (click)="openBulkUpdateStatusModal(BulkUpdateStatusModal)">
      Bulk {{ 'GLOBAL.update' | translate }} {{ 'GLOBAL.status' | translate }}
    </button>
    <button class="btn-bulk-1"
      *ngIf="(currentPath === '/invoice'  ? canBulkReassignInvoice : canBulkReassign ) && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted'"
      (click)="openBulkReassignModal(BulkReassignModal)" id="btnBulkReassign" data-automate-id="btnBulkReassign">{{
      ((leadVisibilityEnum[appliedFilter?.leadVisibility] ===
      'Unassigned') ?
      'LEADS.assign' : 'GLOBAL.reassign' ) | translate }} {{ 'GLOBAL.leads' | translate }}</button>

    <ng-container *ngIf="globalSettingsData?.isDualOwnershipEnabled">
      <button class="btn-bulk-1"
        *ngIf="(currentPath === '/invoice' ? canBulkSecondaryReassignInvoice : canBulkSecondaryReassign) && appliedFilter?.leadVisibility !== 4 && appliedFilter?.leadVisibility!== 3"
        (click)="openBulkSecondaryReassignModal(BulkSecondaryReassignModal)" id="btnBulkSecondaryReassign"
        data-automate-id="btnBulkSecondaryReassign">{{
        ((leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Unassigned') ?
        'Secondary Assign' : 'Secondary Reassign' ) | translate }} {{ 'GLOBAL.leads' | translate }}</button>
    </ng-container>

    <button class="btn-bulk-1"
      *ngIf="(currentPath === '/invoice' ? canBulkUpdateSourceInvoice : canBulkUpdateSource)  && canViewLeadSource && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted' && globalSettingsData?.isLeadSourceEditable"
      [ngClass]="{'blinking pe-none': !isGlobalSettingsFetched}" id="btnBulkSource" data-automate-id="btnBulkSource"
      (click)="openBulkSourceModal(BulkSourceModal)">
      Bulk {{ 'LEADS.source' | translate }}</button>

    <button class="btn-bulk-1"
      *ngIf="(currentPath === '/invoice' ? canBulkProjectAssignmentInvoice : canBulkProjectAssignment) && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted'"
      id="btnBulkProject" data-automate-id="btnBulkProject" (click)="openBulkProjectModal(BulkProjectModal)">
      Bulk {{ 'SIDEBAR.project' | translate }}</button>

    <button class="btn-bulk-1" [ngClass]="{'blinking pe-none': !isGlobalSettingsFetched}"
      *ngIf="(currentPath === '/invoice' ? canBulkWhatsAppInvoice : canBulkWhatsapp) && leadVisibilityEnum[appliedFilter?.leadVisibility]  !== 'Unassigned' && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted' && globalSettingsData?.isWhatsAppEnabled"
      (click)="openBulkShareModal()">Bulk WhatsApp</button>
    <button class="btn-bulk-1" [ngClass]="{'blinking pe-none': !isGlobalSettingsFetched}"
      *ngIf="(currentPath === '/invoice' ? canBulkEmailInvoice : canBulkEmail)  && leadVisibilityEnum[appliedFilter?.leadVisibility]  !== 'Unassigned' && leadVisibilityEnum[appliedFilter?.leadVisibility] !==  'Deleted'"
      (click)="openEmailConfirmation(changePopup,noMail)">Bulk
      Email</button>
    <button class="btn-bulk-1-blue"
      *ngIf="(currentPath === '/invoice' ? canBulkRestoreInvoice : canBulkRestore) && leadVisibilityEnum[appliedFilter?.leadVisibility] ===  'Deleted'"
      (click)="bulkPermanentDelete=true;openBulkDeleteModal(BulkDeleteModal)" id="btnBulkDelete"
      data-automate-id="btnBulkDelete">
      {{ 'LEADS.bulk' | translate }} {{ ('BUTTONS.restore') | translate }}</button>
    <button class="btn-bulk-1-red" *ngIf="currentPath === '/invoice' ? canBulkDeleteInvoice : canBulkDelete"
      (click)="bulkPermanentDelete=false;openBulkDeleteModal(BulkDeleteModal)" id="btnBulkDelete"
      data-automate-id="btnBulkDelete">
      {{ 'LEADS.bulk' | translate }} {{ ('BUTTONS.delete') | translate }}</button>
  </div>
</div>

<ng-template #changePopup>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
    <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
    <div class="flex-end mt-30">
      <button class="btn-gray mr-20" (click)="modalRef.hide()" id="clkSettingsNo" data-automate-id="clkSettingsNo">
        {{ 'GLOBAL.no' | translate }}</button>
      <button class="btn-green" (click)="openBulkEmailPopup()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        {{ 'GLOBAL.yes' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #noMail>
  <div class="p-20">
    <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
    <!-- <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div> -->
    <div class="flex-end mt-30">
      <button class="btn-green" (click)="modalRef.hide()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
        Close</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkUpdateStatusModal>
  <bulk-update-status-leads [modal]="bulkUpdateModalRef" [leadInput]="sameStatusRows"
    (modelOutput)="emitModalOutput($event)" [selectedNodes]="selectedNodes" [gridApi]="gridApi">
  </bulk-update-status-leads>
</ng-template>

<ng-template #BulkReassignModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkReassignLeadsIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'GLOBAL.reassign' | translate }} {{ 'GLOBAL.leads' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedBulkReassign?.length}}</div>
      <div class="scrollbar table-scrollbar">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">
                <div class="justify-between">
                  <span>{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isLeadNameColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('name', isLeadNameColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-120">
                <div class="justify-between">
                  <span>{{ 'LEADS.assign-to' | translate }}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isAssignToColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('assignTo', isAssignToColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-240">
            <ng-container>
              <tr *ngFor="let lead of selectedBulkReassign">
                <td class="w-120" [title]="lead.name">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120" [title]="assignToName(lead)">
                  <div class="text-truncate-1 break-all">
                    {{assignToName(lead)}}
                  </div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead.name, lead.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkRessiagn" data-automate-id="clkDeleteBulkRessiagn">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkReassignForm">
        <div class="d-flex flex-wrap">
          <div *ngFor="let assignmentTypeOption of assignmentTypeOptions" class="form-check form-check-inline p-0 mr-0"
            [ngClass]="{'pe-none': isUnassignLeadSelected}">
            <div class="align-center mt-20 mr-20">
              <input type="radio" [id]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'"
                class="radio-check-input border-remove" [value]="assignmentTypeOption.value"
                formControlName="assignmentType" name="assignmentType" [disabled]="isUnassignLeadSelected"
                (change)="onAssignmentTypeChange(assignmentTypeOption.label)">
              <label class="fw-600 text-dark-800 cursor-pointer text-sm ml-8"
                [for]="assignmentTypeOption?.value + 'assignmentTypeOptionRadio'">
                {{ assignmentTypeOption.label }}
              </label>
            </div>
          </div>
        </div>
        <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
        <div class="text-sm text-black-100 mb-4 pt-2" *ngIf="isDualOwnershipEnabled">Primary</div>
        <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
          label="{{'LEADS.assign-to' | translate}}" class="ng-select-sm-gray">
          <ng-select [virtualScroll]="true" placeholder="Select User" name="user" class="bg-white" ResizableDropdown
            [items]="assignToUsersList" [multiple]="true" [searchable]="true" [closeOnSelect]="false"
            formControlName="assignedToUsers"
            *ngIf="!((canAssignToAny && usersListForReassignmentIsLoading) || (!canAssignToAny && adminsAndReporteesIsLoading)) else fieldLoader"
            (change)="assignToUserListChanged()">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                  *ngIf="!isUnassignLeadSelected || item.value===''" class="checkmark"></span>{{item.label}}</div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
        <h5 class="text-dark-800 fw-600 mt-16">{{'GLOBAL.select-your-preferences' | translate}}</h5>
        <div class="d-flex">
          <label class="checkbox-container mt-12" [ngClass]="{'pe-none': isUnassignLeadSelected}"
            *ngIf="canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
            <input type="checkbox" [checked]="bulkReassignForm.get('isChangeSourceSelected').value" (click)="trackingService.trackFeature('Web.Leads.Options.changeSource.Click')
            " [disabled]="isUnassignLeadSelected" formControlName="isChangeSourceSelected">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-source'
              | translate}}</span>
          </label>
        </div>
        <div class="d-flex w-100 ng-select-sm"
          *ngIf="bulkReassignForm.get('isChangeSourceSelected').value && canUpdateSource && canViewLeadSource && globalSettingsData?.isLeadSourceEditable">
          <div class="w-50">
            <div class="field-label-req text-sm">{{'LEADS.source' | translate}}</div>
            <form-errors-wrapper [control]="bulkReassignForm.controls['selectedSource']"
              label="{{'LEADS.source' | translate}}">
              <ng-select [virtualScroll]="true" placeholder="Select Source" name="source" ResizableDropdown
                formControlName="selectedSource" class="mr-10 bg-white" (change)="onSourceChange()">
                <ng-option *ngFor="let source of leadSources" [value]="source" [disabled]="!source.isEnabled"
                  [ngClass]="{'pe-none': !source.isEnabled}">
                  <div class="dropdown-position">
                    <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
                    <span class="text-disabled" *ngIf="!source.isEnabled"> (Disabled)</span>
                  </div>
                </ng-option>
              </ng-select>
            </form-errors-wrapper>
          </div>
          <div class="w-50 ml-10">
            <div class="field-label text-sm">{{'LEADS.sub-source' | translate}}</div>
            <ng-select [virtualScroll]="true" *ngIf="!subSourceListIsLoading else fieldLoader" ResizableDropdown
              placeholder="Select Sub-Source" name="source" formControlName="selectedSubSource" class="bg-white"
              [addTag]="true">
              <ng-option *ngFor="let subSource of subSourceList" [value]="subSource">
                {{ subSource }}
              </ng-option>
            </ng-select>
          </div>
        </div>
        <div class="d-flex">
          <label class="checkbox-container mt-16" [ngClass]="{'pe-none': isUnassignLeadSelected}">
            <input type="checkbox" [checked]="bulkReassignForm.get('isChangeProjectSelected').value"
              (click)="trackingService.trackFeature('Web.Leads.Options.ChangeProject.Click')"
              [disabled]="isUnassignLeadSelected" formControlName="isChangeProjectSelected">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.change-project'
              | translate}}</span>
          </label>
        </div>
        <div *ngIf="bulkReassignForm.get('isChangeProjectSelected').value" class="w-50">
          <div class="field-label-req text-sm">{{'GLOBAL.project-name' | translate}}</div>
          <form-errors-wrapper [control]="bulkReassignForm.controls['selectedProject']"
            label="{{'GLOBAL.project-name' | translate}}" class="ng-select-sm-gray">
            <ng-select [virtualScroll]="true" placeholder="Select Project" name="project" class="bg-white"
              ResizableDropdown [items]="projectList" *ngIf="!projectListIsLoading else fieldLoader" [multiple]="true"
              [searchable]="true" [closeOnSelect]="false" formControlName="selectedProject">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="project-{{index}}"
                    data-automate-id="project-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                    class="text-truncate-1 break-all">{{item}}</span></div>
              </ng-template>
            </ng-select>
          </form-errors-wrapper>
        </div>
        <div class="d-flex">
          <label *ngIf="isDuplicateFeature" class="checkbox-container mt-16"
            [ngClass]="{'pe-none': isUnassignLeadSelected, 'blinking pe-none': !isDuplicateFeatureSettingsFetched}">
            <input type="checkbox" [checked]="bulkReassignForm.get('isCreateDuplicateSelected').value"
              (click)="trackingService.trackFeature('Web.Leads.Button.CreateDuplicate.Click')"
              [disabled]="isUnassignLeadSelected" formControlName="isCreateDuplicateSelected"
              [attr.disabled]="isDuplicateFeatureSettingsFetched && !isDuplicateFeatureAdded ? true : null">
            <span class="checkmark"></span> <span class="fw-600 text-sm">{{'GLOBAL.create-duplicate'
              | translate}}</span> <span class="fw-semi-bold text-xs text-dark-800 ml-10"
              *ngIf="isDuplicateFeatureSettingsFetched && !isDuplicateFeatureAdded">Disabled</span>
            <div class="fw-semi-bold text-xs text-mud">This will create a duplicate of the current lead(s)</div>
          </label>
        </div>
      </form>
    </div>
    <div class="flex-center mt-20" *ngIf="!bulkReassignLeadsIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkReassign" data-automate-id="btnSaveBulkReassign"
        (click)="updateBulkAssign('bulkReassign')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkSecondaryReassignModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkReassignLeadsIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} Secondary {{ 'GLOBAL.reassign' | translate }} {{ 'GLOBAL.leads' | translate }}
      </h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 scrollbar h-100-108 ph-h-100-170">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedBulkReassign?.length}}</div>
      <div class="scrollbar table-scrollbar">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">
                <div class="flex-between">
                  <span>{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isLeadNameColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('name', isLeadNameColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-120">
                <div class="flex-between">
                  <span>{{ 'LEADS.assign-to' | translate }}</span>
                  <span class="icon ic-xx-xs my-auto cursor-pointer"
                    [ngClass]="!isAssignToColInAscOrder ? 'ic-arrow-down' : 'ic-arrow-up'"
                    (click)="sortColumn('secondaryUserId', isAssignToColInAscOrder)"></span>
                </div>
              </th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold scrollbar max-h-100-265">
            <ng-container>
              <tr *ngFor="let lead of selectedBulkReassign">
                <td class="w-120" [title]="lead.name">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120" [title]="secondaryAssignToName(lead)">
                  <div class="align-center" *ngIf="lead?.secondaryUserId && lead?.secondaryUserId !== EMPTY_GUID">
                    <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
                    <p class="text-truncate-1 break-all">{{secondaryAssignToName(lead)}}</p>
                  </div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead.name, lead.id)" class="bg-light-red icon-badge"
                    id="clkDeleteSecondaryBulkRessiagn" data-automate-id="clkDeleteSecondaryBulkRessiagn">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <div class="field-label-req mt-16">{{ 'LEADS.assign-to' | translate }}</div>
      <div class="ng-select-sm-gray">
        <!-- <form-errors-wrapper [control]="bulkReassignForm.controls['assignedToUsers']"
          label="{{'LEADS.assign-to' | translate}}"> -->
        <form [formGroup]="bulkSecondaryReassignForm">
          <div class="text-sm text-black-100 mb-4 pt-2">secondary</div>
          <form-errors-wrapper [control]="bulkSecondaryReassignForm.controls['secondary']" label="secondary">
            <ng-select placeholder="Select User" name="user" class="bg-white" [items]="assignToUsersList"
              ResizableDropdown formControlName="secondary" [multiple]="true" [searchable]="true"
              [closeOnSelect]="false"
              *ngIf="!((canAssignToAny && usersListForReassignmentIsLoading) || (!canAssignToAny && adminsAndReporteesIsLoading)) else fieldLoader">
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                    *ngIf="!isUnassignLeadSelected || item.value===''" class="checkmark"></span>{{item.label}}</div>
              </ng-template>
            </ng-select>
          </form-errors-wrapper>
        </form>
        <!-- </form-errors-wrapper> -->
      </div>
    </div>
    <div class="flex-center mt-20" *ngIf="!bulk2ndReassignIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveSecondaryBulkReassign" data-automate-id="btnSaveSecondaryBulkReassign"
        (click)="updateSecondaryBulkAssign('bulkReassign')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkSourceModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern" [ngClass]="{'pe-none': bulkSourceIsLoading}">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'LEADS.source' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedSources?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120"> {{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{ 'LEADS.source' | translate }}</th>
              <th class="w-120 text-nowrap">{{ 'LEADS.sub-source' | translate }}</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold  max-h-100-332 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedSources">
                <td class="w-120">
                  <div class="text-truncate-1 break-all"> {{ lead.name }} </div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ LeadSource[lead.enquiry?.leadSource] }}</div>
                </td>
                <td class="w-120">
                  <div class="text-truncate-1 break-all">{{ lead.enquiry?.subSource }}</div>
                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkSource" data-automate-id="clkDeleteBulkSource">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkSourceForm" class="ng-select-sm">
        <div class="field-label-req">{{ 'LEADS.source' | translate }}</div>
        <form-errors-wrapper [control]="bulkSourceForm.controls['source']" label="source">
          <ng-select [virtualScroll]="true" placeholder="Select Source" name="source" ResizableDropdown
            formControlName="source"
            (change)="updateSubSourceForBulkSource(); trackingService.trackFeature('Web.Leads.Options.Source.Click')">
            <ng-option *ngFor="let source of leadSources" [value]="source" [disabled]="!source.isEnabled">
              <div class="dropdown-position">
                <span class="text-truncate-1 break-all">{{ source.displayName }}</span>
              </div>
            </ng-option>
          </ng-select>
        </form-errors-wrapper>
        <ng-container *ngIf="bulkSourceForm.get('source').value">
          <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
          <ng-select [virtualScroll]="true" [items]="subSourceList" [addTag]="true" addTagText="Create New sub-source"
            ResizableDropdown formControlName="subsource"
            placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'LEADS.sub-source' | translate }}">
          </ng-select>
        </ng-container>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkSourceIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkSource" data-automate-id="btnSaveBulkSource"
        (click)="updateBulkSource('bulkSource')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkProjectModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>{{ 'LEADS.bulk' | translate }} {{ 'PROJECTS.projects' | translate }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalService.hide()"></div>
    </div>
    <div class="px-24 h-100-114">
      <div class="fw-600 text-coal text-large my-8">{{'LEADS.selected-lead' | translate}} -
        {{selectedProjects?.length}}</div>
      <div class="scrollbar table-scrollbar ph-w-100-60">
        <table class="table standard-table no-vertical-border">
          <thead>
            <tr class="w-100">
              <th class="w-120">{{'GLOBAL.lead' | translate}} {{'GLOBAL.name' | translate}}</th>
              <th class="w-120">{{ 'PROJECTS.projects' | translate }}</th>
              <th class="w-70px">{{ 'GLOBAL.actions' | translate }}</th>
            </tr>
          </thead>
          <tbody class="text-secondary fw-semi-bold max-h-100-250 scrollbar">
            <ng-container>
              <tr *ngFor="let lead of selectedProjects">
                <td class="w-120"><span class="text-truncate-1 break-all">{{ lead.name }}</span></td>
                <td class="w-120">
                  <div class="d-flex text-truncate-1 break-all">
                    <span *ngFor="let project of lead.projects; let i = index">
                      <span class="text-nowrap" [title]="getProjectNames(lead)">{{ project.name }}</span>
                      <span *ngIf="i < lead.projects.length - 1">, </span>
                    </span>
                  </div>

                </td>
                <td class="w-70px">
                  <a (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
                    id="clkDeleteBulkProject" data-automate-id="clkDeleteBulkProject">
                    <span class="icon ic-cancel m-auto ic-xx-xs"></span></a>
                </td>
              </tr>
            </ng-container>
          </tbody>
        </table>
      </div>
      <form [formGroup]="bulkProjectFrom">
        <div class="mt-20">
          <div class="justify-between">
            <div class="field-label mt-0">{{'PROJECTS.projects'| translate}}</div>
            <label class="checkbox-container mb-4">
              <input type="checkbox" formControlName="ShouldRemoveExistingProjects"
                (click)="trackingService.trackFeature('Web.Leads.Button.RemoveExistingProjects.Click')">
              <span class="checkmark"></span>Remove Existing Project(s)
            </label>
          </div>
        </div>
        <form-errors-wrapper [control]="bulkProjectFrom.controls['project']" label="project">
          <ng-select [virtualScroll]="true" [items]="projectList" *ngIf="!projectListIsLoading else fieldLoader"
            ResizableDropdown [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
            (change)="trackingService.trackFeature('Web.Leads.Options.projects.Click')" formControlName="project"
            class="bg-white">
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                  data-automate-id="item-{{index}}" [checked]="item$.selected"><span class="checkmark"></span><span
                  class="text-truncate-1 break-all">{{item}}</span>
              </div>
            </ng-template>
          </ng-select>
        </form-errors-wrapper>
      </form>
    </div>
    <div class="flex-center mt-20 mb-40" *ngIf="!bulkProjectsIsLoading else ratLoader">
      <button class="btn-gray mr-20" (click)="modalService.hide()">{{ 'BUTTONS.cancel' | translate }}</button>
      <button class="btn-coal" id="btnSaveBulkProject" data-automate-id="btnSaveBulkProject"
        (click)="updateBulkProjects('bulkProject')">{{ 'BUTTONS.save' | translate }}</button>
    </div>
  </div>
</ng-template>

<ng-template #BulkDeleteModal>
  <div class="bg-light-pearl h-100vh bg-triangle-pattern">
    <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
      <h3>Bulk {{ leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Deleted' && bulkPermanentDelete? 'Restore' :
        'Delete' }}</h3>
      <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="bulkDeleteModalRef.hide()"></div>
    </div>
    <div class="px-12">
      <div class="field-label mb-10">{{'LEADS.selected-lead' | translate}} -
        {{selectedLeads?.length}}
      </div>
      <div class="flex-column scrollbar max-h-100-176">
        <ng-container *ngFor="let lead of selectedLeads">
          <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
            <span class="text-truncate-1 break-all">{{ lead.name }}</span>
            <div (click)="openConfirmDeleteModal(lead?.name, lead?.id)" class="bg-light-red icon-badge"
              id="clkBulkDelete" data-automate-id="clkBulkDelete">
              <span class="icon ic-cancel m-auto ic-xx-xs"></span>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="flex-center" *ngIf="(!bulkDeleteIsLoading && !bulkRestoreIsLoading) else ratLoader">
        <button class="btn-coal mt-20" (click)="updateBulkDelete()">{{ (
          leadVisibilityEnum[appliedFilter?.leadVisibility] === 'Deleted' &&
          bulkPermanentDelete ?
          'BUTTONS.restore' : 'BUTTONS.delete') | translate }}</button>
      </div>
    </div>
  </div>
</ng-template>


<ng-template #fieldLoader>
  <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>

<ng-template #ratLoader>
  <div class="flex-center w-100 my-8">
    <img src="assets/images/loader-rat.svg" class="rat-loader h-20px w-20px" alt="loader">
  </div>
</ng-template>

<ng-template #trackerInfoModal>
  <h5 class="px-20 py-16 fw-semi-bold bg-coal text-white">{{ type }} </h5>
  <div class="p-20 flex-center-col">
    <h4 class="text-black-100 fw-600 mb-10 text-center word-break line-break">{{type}} in progress
    </h4>
    <h5 class="text-black-100 fw-semi-bold text-center word-break line-break">You can check
      <span class="cursor-pointer text-accent-green header-3 fw-600" (click)="openBulkStatus()">“Bulk
        Operation
        Tracker”</span> to view status
    </h5>
    <button class="btn-green mt-30" (click)="modalService.hide()">
      {{'BULK_LEAD.got-it' | translate}}</button>
  </div>
</ng-template>