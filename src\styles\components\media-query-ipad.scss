@media screen and (max-width: 768px) {
    .ip-scrollbar {
        overflow: auto !important;

        &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }

        &::-webkit-scrollbar-track {
            background: #49494914;
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: $dark-700;
            border-radius: 3px;
        }
    }

    .ip-w-30px {
        width: 30px !important;
    }

    .ip-w-33 {
        width: 33% !important;
    }

    .ip-w-45 {
        width: 45px !important;
    }

    .ip-w-50 {
        width: 50% !important;
    }

    .ip-w-60 {
        width: 60% !important;
    }

    .ip-w-100 {
        width: 100% !important;
    }

    .ip-w-45 {
        width: 45px !important;
    }

    .ip-w-50px {
        width: 50px !important;
    }

    .ip-w-80px {
        width: 80px !important;
    }

    .ip-w-100px {
        width: 100px !important;
    }

    .ip-w-170 {
        width: 170px !important;
    }

    .ip-w-200px {
        width: 200px !important;
    }

    .ip-w-350 {
        width: 350px !important;
    }

    .ip-w-360 {
        width: 360px !important;
    }

    .ip-max-w-80px {
        max-width: 80px !important;
    }

    .ip-w-100-15 {
        width: calc(100vw - 15px) !important;
    }

    .ip-w-100-27 {
        width: calc(100vw - 27px) !important;
    }

    .ip-w-100-30 {
        width: calc(100vw - 30px) !important;
    }

    .ip-w-100-40 {
        width: calc(100vw - 40px) !important;
    }

    .ip-w-100-45 {
        width: calc(100vw - 45px) !important;
    }

    .ip-w-100-60 {
        width: calc(100vw - 60px) !important;
    }

    .ip-w-100-70 {
        width: calc(100vw - 70px) !important;
    }

    .ip-w-100-90 {
        width: calc(100vw - 90px) !important;
    }

    .ip-w-100-96 {
        width: calc(100vw - 96px) !important;
    }

    .ip-w-100-120 {
        width: calc(100vw - 120px) !important;
    }

    .ip-w-100-145 {
        width: calc(100vw - 145px) !important;
    }

    .ip-w-100-190 {
        width: calc(100vw - 190px) !important;
    }

    .ip-w-100-215 {
        width: calc(100vw - 215px) !important;
    }

    .ip-w-100-225 {
        width: calc(100vw - 225px) !important;
    }

    .ip-w-100-300 {
        width: calc(100vw - 300px) !important;
    }

    .ip-w-100-315 {
        width: calc(100vw - 315px) !important;
    }

    .ip-max-w-200 {
        max-width: 200px !important;
    }


    .ip-max-w-350 {
        max-width: 350px !important;
    }

    .ip-max-w-340 {
        max-width: 340px;
    }

    .ip-min-w-350 {
        min-width: 350px !important;
    }

    .ip-max-w-100-70 {
        max-width: calc(100vw - 70px) !important;
    }

    .ip-max-w-100-130 {
        max-width: calc(100vw - 130px) !important;
    }

    .ip-width-unset {
        min-width: unset !important;
        max-width: unset !important;
    }

    .ip-h-60 {
        height: 60px !important;
    }

    .ip-h-100-320 {
        height: calc(100dvh - 320px) !important;
    }

    .ip-h-100-354 {
        height: calc(100dvh - 354px) !important;
    }

    .ip-h-100-390 {
        height: calc(100dvh - 390px) !important;
    }

    .ip-mt-0 {
        margin-top: 0px !important;
    }

    .ip-mt-4 {
        margin-top: 4px !important;
    }

    .ip-mt-10 {
        margin-top: 10px !important;
    }

    .ip-mt-20 {
        margin-top: 20px !important;
    }

    .ip-mt-40 {
        margin-top: 40px !important;
    }

    .ip-mt-60 {
        margin-top: 60px !important;
    }

    .ip-mr-0 {
        margin-right: 0px !important;
    }

    .ip-mr-4 {
        margin-right: 4px !important;
    }

    .ip-mr-10 {
        margin-right: 10px !important;
    }

    .ip-mr-20 {
        margin-right: 20px !important;
    }

    .ip-mr-40 {
        margin-right: 40px !important;
    }

    .ip-mb-4 {
        margin-bottom: 4px;
    }

    .ip-mb-10 {
        margin-bottom: 10px;
    }

    .ip-mb-20 {
        margin-bottom: 20px;
    }

    .ip-ml-0 {
        margin-left: 0px !important;
    }

    .ip-ml-10 {
        margin-left: 10px !important;
    }

    .ip-ml-16 {
        margin-left: 16px !important;
    }

    .ip-mx-0 {
        margin-right: 0px !important;
        margin-left: 0px !important;
    }

    .ip-mx-10 {
        margin-right: 10px !important;
        margin-left: 10px !important;
    }

    .ip-p-0 {
        padding: 0px !important;
    }

    .ip-p-8 {
        padding: 8px !important;
    }

    .ip-px-4 {
        padding-left: 4px !important;
        padding-right: 4px !important;
    }

    .ip-px-8 {
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .ip-pl-0 {
        padding-left: 0px !important;
    }

    .ip-pl-10 {
        padding-left: 10px !important;
    }

    .ip-px-20 {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .ip-px-40 {
        padding-left: 40px !important;
        padding-right: 40px !important;
    }

    .ip-pt-30 {
        padding-top: 30px !important;
    }

    .ip-pb-10 {
        padding-bottom: 10px;
    }

    .ip-pr-0 {
        padding-right: 0px !important;
    }

    .ip-pr-20 {
        padding-right: 20px !important;
    }

    .ip-top-10 {
        top: 10px !important;
    }

    .ip-top-11 {
        top: 11px !important;
    }

    .ip-top-100 {
        top: 100px !important;
    }

    .ip-right-0 {
        right: 0 !important;
    }

    .ip-right-5 {
        right: 5px !important;
    }

    .ip-left-30 {
        position: relative;
        left: 30px !important;
    }

    .ip-left-100px {
        left: 100px !important;
    }

    .ip-bottom {
        position: absolute !important;
        right: 0px !important;
        bottom: -50px !important;
        margin-bottom: 10px;
        text-align: center !important;
        width: 100%;
    }

    .ip-d-flex {
        display: flex !important;
        flex-direction: row !important;
    }

    .ip-flex-wrap {
        flex-wrap: wrap;
    }

    .ip-d-none {
        display: none !important;
    }

    .ip-d-block {
        display: block !important;
    }

    .ip-d-flex {
        display: flex !important;
    }

    .ip-flex-col {
        flex-direction: column;
    }

    .ip-flex-grow-1 {
        flex-grow: 1 !important;
    }

    .ip-col-reverse {
        flex-direction: column-reverse !important;
    }

    .ip-flex-start {
        align-items: flex-start !important;
    }

    .ip-flex-end {
        align-items: flex-end !important;
        justify-content: end !important;
    }


    .ip-flex-center-col {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .ip-flex-between-unset {
        align-items: unset !important;
        justify-content: unset !important;
    }

    .ip-flex-center {
        align-items: center !important;
        justify-content: center !important;
    }

    .ip-flex-center-col {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .ip-justify-end {
        justify-content: flex-end !important;
    }

    .ip-center {
        display: block;
        margin-left: auto;
        margin-right: auto !important;
        width: 50%;
    }

    .ip-align-center-unset {
        align-items: unset !important;
    }

    .ip-flex-center-unset {
        align-items: unset !important;
        justify-content: unset !important;
    }

    .ip-text-center {
        text-align: center !important;
    }

    .ip-modal-unset {
        width: unset !important;
        min-width: unset !important;
    }

    .ip-h-auto {
        height: auto !important;
        margin-left: auto !important;
        margin-right: auto !important;
        width: 100%;
    }

    .ip-ic-black {
        color: $black;
    }

    .ip-ic-close-modal {
        color: $black;
        right: 5px !important;
        top: 5px !important;
    }

    .ip-ic-close-secondary {
        right: 5px !important;
        top: 5px !important;
    }

    .ip-bg-white {
        background-color: $white !important;
    }

    .ip-fw-400 {
        font-weight: 400 !important;
    }

    .ip-br-0 {
        border-right: none !important;
    }

    .ip-br-top {
        border-top: 1px solid $dark-400;
    }

    .ip-br-lr {
        border-left: 1px solid $dark-400;
        border-right: 1px solid $dark-400;
    }

    .ip-bg-unset {
        background-color: unset !important;
    }

    .ip-text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}