@media screen and (max-width: 1279px) {
  .tb-scrollbar {
    overflow: auto !important;

    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background: #49494914;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: $dark-700;
      border-radius: 3px;
    }
  }

  .tb-top-navbar {
    border-top: 1px solid $dark-400;
    background-color: $white;
    padding: 12px 30px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    position: unset !important;
    top: unset !important;
    right: unset !important;
  }

  .tb-w-20 {
    width: 20% !important;
  }

  .tb-w-25 {
    width: 25% !important;
  }

  .tb-w-33 {
    width: 33% !important;
  }

  .tb-w-50 {
    width: 50% !important;
  }

  .tb-w-75 {
    width: 75% !important;
  }

  .tb-w-100 {
    width: 100% !important;
  }

  .tb-w-120px {
    width: 120px !important;
  }

  .tb-w-200 {
    width: 200px !important;
  }

  .tb-w-360px {
    width: 360px !important;
  }

  .tb-w-unset {
    width: unset !important;
  }

  .tb-max-w-unset {
    max-width: unset !important;
  }

  .tb-min-w-unset {
    min-width: unset !important;
  }

  .tb-h-unset {
    height: unset !important;
  }

  .tb-w-100-0 {
    overflow: auto;
    width: calc(100vw - 0px) !important;
  }

  .tb-w-100-24 {
    width: calc(100vw - 24px) !important;
  }

  .tb-w-100-30 {
    width: calc(100vw - 30px) !important;
  }

  .tb-w-100-34 {
    overflow: auto;
    width: calc(100vw - 34px) !important;
  }

  .tb-w-100-40 {
    overflow: auto;
    width: calc(100vw - 40px) !important;
  }

  .tb-max-w-100-40 {
    overflow: auto;
    max-width: calc(100vw - 40px);
  }

  .tb-w-100-50 {
    width: calc(100vw - 50px) !important;
  }

  .tb-w-100-57 {
    width: calc(100vw - 57px) !important;
  }

  .tb-w-100-60 {
    width: calc(100vw - 60px) !important;
  }

  .tb-w-100-66 {
    width: calc(100vw - 66px) !important;
  }

  .tb-w-100-70 {
    width: calc(100vw - 70px) !important;
  }

  .tb-w-100-80 {
    width: calc(100vw - 80px) !important;
  }

  .tb-w-100-100px {
    width: calc(100vw - 100px) !important;
  }

  .tb-w-100-113 {
    width: calc(100vw - 113px) !important;
  }

  .tb-w-100-135 {
    width: calc(100vw - 135px) !important;
  }

  .tb-w-100-143 {
    width: calc(100vw - 143px) !important;
  }

  .tb-w-100-160 {
    width: calc(100vw - 160px) !important;
  }

  .tb-w-100-180 {
    width: calc(100vw - 180px) !important;
  }

  .tb-w-100-190 {
    width: calc(100vw - 190px) !important;
  }

  .tb-w-100-200 {
    width: calc(100vw - 200px) !important;
  }

  .tb-w-100-210 {
    width: calc(100vw - 210px) !important;
  }

  .tb-w-100-220 {
    width: calc(100vw - 220px) !important;
  }

  .tb-w-100-230 {
    width: calc(100vw - 230px) !important;
  }

  .tb-w-100-240 {
    width: calc(100vw - 240px) !important;
  }

  .tb-w-100-250 {
    width: calc(100vw - 250px) !important;
  }

  .tb-w-100-280 {
    width: calc(100vw - 280px) !important;
  }

  .tb-w-100-300 {
    width: calc(100vw - 300px) !important;
  }

  .tb-w-100-320 {
    width: calc(100vw - 320px) !important;
  }

  .tb-w-100-380 {
    width: calc(100vw - 380px) !important;
  }

  .tb-w-100-400 {
    width: calc(100vw - 400px) !important;
  }

  .tb-max-w-80 {
    max-width: 80px !important;
  }

  .tb-max-w-100-190 {
    max-width: calc(100vw - 190px) !important;
  }

  .tb-max-w-100-230 {
    max-width: calc(100vw - 230px) !important;
  }

  .tb-h-100-30 {
    overflow: auto;
    height: calc(100dvh - 30px) !important;
  }

  .tb-h-100-108 {
    height: calc(100dvh - 108px) !important;
  }

  .tb-mt-0 {
    margin-top: 0 !important;
  }

  .tb-mt-10 {
    margin-top: 10px !important;
  }

  .tb-mt-16 {
    margin-top: 16px !important;
  }

  .tb-mt-20 {
    margin-top: 20px;
  }

  .tb-mr-0 {
    margin-right: 0 !important;
  }

  .tb-mr-10 {
    margin-right: 10px !important;
  }

  .tb-mr-20 {
    margin-right: 20px;
  }

  .tb-mr-30 {
    margin-right: 30px !important;
  }

  .tb-mb-0 {
    margin-bottom: 0 !important;
  }

  .tb-mb-10 {
    margin-bottom: 10px !important;
  }

  .tb-mb-20 {
    margin-bottom: 20px;
  }

  .tb-mb-65 {
    margin-bottom: 65px;
  }

  .tb-mb-100 {
    margin-bottom: 100px !important;
  }

  .tb-ml-0 {
    margin-left: 0px !important;
  }

  .tb-ml-10 {
    margin-left: 10px !important;
  }

  .tb-ml-20 {
    margin-left: 20px !important;
  }

  .tb-ml-30 {
    margin-left: 30px !important;
  }

  .tb-ml-40 {
    margin-left: 40px !important;
  }

  .tb-ml-50 {
    margin-left: 50px !important;
  }

  .tb-mx-40 {
    margin-left: 40px !important;
    margin-right: 40px !important;
  }

  .tb-my-0 {
    margin-top: 0px !important;
    margin-bottom: 0px !important;
  }

  .tb-px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .tb-px-10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .tb-px-40 {
    padding-left: 40px !important;
    padding-right: 40px !important;
  }

  .tb-py-10 {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
  }

  .tb-pt-10 {
    padding-top: 10px !important;
  }

  .tb-pr-0 {
    padding-right: 0 !important;
  }

  .tb-pr-20 {
    padding-right: 20px !important;
  }

  .tb-pr-40 {
    padding-right: 40px !important;
  }

  .tb-pl-40 {
    padding-left: 40px !important;
  }

  .tb-pb-0 {
    padding-bottom: 0 !important;
  }

  .tb-position-absolute {
    position: absolute;
  }

  .tb-top-12 {
    top: 12px !important;
  }

  .tb-nleft-50 {
    left: -50px !important;
  }

  .tb-right-0 {
    right: 0px;
  }

  .tb-right-20 {
    right: 20px !important;
  }

  .tb-left-32 {
    left: 32px !important;
  }

  .tb-left-110 {
    left: 110px !important;
  }

  .tb-left-130 {
    left: 130px !important;
  }

  .tb-left-165 {
    left: 165px !important;
  }

  .tb-left-175 {
    left: 175px !important;
  }

  .tb-left-200 {
    left: 200px !important;
  }

  .tb-bottom-15 {
    bottom: -15px !important;
  }

  .tb-d-none {
    display: none !important;
  }

  .tb-d-block {
    display: block !important;
  }

  .tb-max-w-unset {
    max-width: unset !important;
  }

  .tb-d-flex {
    display: flex !important;
  }

  .tb-flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }

  .tb-flex-col {
    flex-direction: column;
  }

  .tb-flex-end {
    align-items: flex-end !important;
    justify-content: flex-end;
  }

  .tb-flex-center {
    justify-content: center !important;
    align-items: center !important;
  }

  .tb-flex-center-unset {
    justify-content: unset !important;
    align-items: unset !important;
  }

  .tb-flex-wrap {
    flex-wrap: wrap;
  }

  .tb-flex-grow-1 {
    flex-grow: 1 !important;
  }

  .tb-col-reverse {
    flex-direction: column-reverse !important;
  }

  .tb-justify-center {
    display: flex;
    justify-content: center !important;
  }

  .tb-align-center-unset {
    align-items: unset !important;
  }

  .tb-max-h-unset {
    max-height: unset !important;
  }

  .tb-text-center {
    text-align: center;
  }

  .tb-br {
    border: 1px solid $dark-400;
  }

  .tb-br-top {
    border-top: 1px solid $dark-400;
  }

  .tb-br-lr {
    border-left: 1px solid $dark-400;
    border-right: 1px solid $dark-400;
  }

  .tb-br-top-unset {
    border-top: unset !important;
  }

  .tb-br-0 {
    border-right: none !important;
  }

  .tb-brt-10 {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    border-bottom-left-radius: unset !important;
  }

  .tb-ic-close-secondary {
    right: 5px !important;
    top: 5px !important;
  }

  .tb-left-nav {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1022;
  }

  .tb-hamburger {
    @extend .tb-left-nav;
    top: 10px;
    left: 10px;
  }

  .footer {
    position: fixed !important;
    left: 0px !important;
    bottom: 0px !important;
    background-color: $white !important;
    color: $white !important;
    text-align: center !important;
    width: 100%;
  }

  .tb-modal-unset {
    min-width: unset !important;
    width: unset !important;
  }

  .tb-ic-x-xs {
    height: 8px;
    width: 8px;
    font-size: 8px;
  }

  .tb-ic-xxs {
    height: 11px !important;
    width: 11px !important;
    font-size: 12px !important;
  }

  .tb-top-18 {
    top: 18px !important;
  }

  .tb-bg-dark {
    background-color: $black !important;
  }

  .tb-bg-unset {
    background-color: unset !important;
  }

  .tb-bg-mixed-pattern {
    background-image: unset;
    background-repeat: unset;
    background-size: unset;
  }

  .tb-text-large {
    font-size: $base-font-size + 1px;
    line-height: $base-font-size + 5px;
  }
}