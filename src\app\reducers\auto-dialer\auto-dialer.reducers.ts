import { Action, createSelector } from "@ngrx/store";
import { AppState } from "src/app/app.reducer";
import { AutoDialerActionTypes, FetchAutoDialerSuccess, FetchStatusCountSuccess } from "./auto-dialer.actions";

export type AutoDialerState = {
    autoDialer: any;
    statusCount: any;
};

const initialState: AutoDialerState = {
    autoDialer: [],
    statusCount: [],
};

export function autoDialerReducer(
    state: AutoDialerState = initialState,
    action: Action
): AutoDialerState {
    switch (action.type) {
        case AutoDialerActionTypes.FETCH_AUTO_DIALER_SUCCESS:
            return {
                ...state,
                autoDialer: (action as FetchAutoDialerSuccess).response,
            };
        case AutoDialerActionTypes.FETCH_STATUS_COUNT_SUCCESS:
            return {
                ...state,
                statusCount: (action as FetchStatusCountSuccess).response,
            };
        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.autoDialer;

export const getAutoDialer = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.autoDialer
);

export const getStatusCount = createSelector(
    selectFeature,
    (state: AutoDialerState) => state.statusCount
);
