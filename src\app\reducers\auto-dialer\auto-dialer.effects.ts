import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Store } from "@ngrx/store";
import { NotificationsService } from "angular2-notifications";
import { of } from "rxjs";
import { catchError, map, switchMap } from "rxjs/operators";
import { OnError } from "src/app/app.actions";
import { AppState } from "src/app/app.reducer";
import { AutodialerService } from "src/app/services/controllers/autodialer.service";
import { AutoDialerActionTypes, FetchAutoDialer, FetchAutoDialerSuccess, FetchStatusCount, FetchStatusCountSuccess } from "./auto-dialer.actions";

@Injectable()
export class AutoDialerEffects {
    getAutoDialer$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.FETCH_AUTO_DIALER),
            map((action: FetchAutoDialer) => action),
            switchMap((data: any) => {
                return this.api.getAutoDialer().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchAutoDialerSuccess(resp);
                        }
                        return new FetchAutoDialerSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getStatusCount$ = createEffect(() =>
        this.actions$.pipe(
            ofType(AutoDialerActionTypes.FETCH_STATUS_COUNT),
            map((action: FetchStatusCount) => action),
            switchMap((data: any) => {
                return this.api.getStatusCount().pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchStatusCountSuccess(resp);
                        }
                        return new FetchStatusCountSuccess([]);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions$: Actions,
        private api: AutodialerService,
        private _notificationService: NotificationsService,
        private _store: Store<AppState>
    ) { }
}