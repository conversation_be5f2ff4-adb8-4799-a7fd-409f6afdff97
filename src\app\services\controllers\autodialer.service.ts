import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
@Injectable({
  providedIn: 'root'
})
export class AutodialerService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'autodailer';
  }

  getAutoDialer() {
    return this.http.get(`${this.serviceBaseUrl}`);
  }

  getStatusCount() {
    return this.http.get(`${this.serviceBaseUrl}/statusescount`);
  }
}
