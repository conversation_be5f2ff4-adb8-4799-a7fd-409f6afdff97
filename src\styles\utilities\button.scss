.btn {
  @extend .text-center, .border-0, .shadow-none, .align-middle, .text-normal;
  outline: none !important;
  padding: 0 16px;
  border-radius: 5px;
  height: 40px;

  &.btn-xxs {
    height: 24px;
    padding: unset;
  }

  &.btn-xs {
    height: 28px;
    padding: unset;
  }

  &.btn-sm {
    height: 32px;
    padding: unset;
  }

  &.btn-md {
    height: 36px;
    padding: unset;
  }

  &.btn-large {
    height: 44px;
    padding: unset;
  }

  &.btn-lg {
    height: 48px;
    padding: unset;
  }

  &.btn-critical,
  &.btn-critical:hover {
    color: $dark-red;
    border: 1px solid $dark-red !important;
  }

  &.btn-high,
  &.btn-high:hover {
    color: $light-orange;
    border: 1px solid $light-orange !important;
  }

  &.btn-medium,
  &.btn-medium:hover {
    color: $yellow-300;
    border: 1px solid $yellow-300 !important;
  }

  &.btn-low,
  &.btn-low:hover {
    color: $light-green;
    border: 1px solid $light-green !important;
  }
}

.btn-check-critical:checked~.btn-critical {
  background-color: $dark-red;
  color: $white;
}

.btn-check-high:checked~.btn-high {
  background-color: $light-orange;
  color: $white;
}

.btn-check-medium:checked~.btn-medium {
  background-color: $yellow-300;
  color: $white;
}

.btn-check-low:checked~.btn-low {
  background-color: $light-green;
  color: $white;
}

.btn-accent-green {
  background-color: $accent-green;
  @extend .text-white;

  &:hover,
  &:focus {
    background-color: $accent-green !important;
  }
}

.btn-accent-green-xl {
  @extend .btn-accent-green,
  .br-10,
  .flex-center,
  .fw-semi-bold,
  .py-16,
  .cursor-pointer;
}

.btn-linear-green {
  background: linear-gradient(119.31deg,
      $accent-green-100 0%,
      $accent-green-200 76.04%);
  @extend .text-white, .align-center;
  padding: 0px 12px !important;
}

.btn-transparent {
  background-color: transparent;
  border: 1px solid $border-light-grey !important;
  color: $slate-60 !important;

  &:hover,
  &:focus {
    color: $slate-60 !important;
  }
}

.btn-residential {
  background-color: $aqua-150;
}

.btn-residential-active {
  background-color: $blue-650;
  border: 1px solid $aqua-350 !important;
  font-weight: 600 !important;
}

.btn-commercial {
  background-color: $aqua-250;
}

.btn-commercial-active {
  background-color: $violet-300;
  border: 1px solid $purple-900 !important;
  font-weight: 600 !important;
}

.btn-agricultural {
  background-color: $aqua-900;
}

.btn-agricultural-active {
  background-color: $green-650;
  border: 1px solid $green-60 !important;
  font-weight: 600 !important;
}

.btn-fb {
  background-color: #1977f3;
  font-family: Helvetica, Arial, sans-serif;
  color: $white;
  font-weight: 700;
  border-radius: 6px;
  font-size: 10px;
  line-height: 11px;

  &:hover,
  &:focus {
    background-color: #1977f3 !important;
    color: $white !important;
  }
}

.form-check-input {
  @extend .mt-0;
}

.form-check-input {
  box-shadow: none !important;
}

.form-check-input:checked {
  background-color: $accent-green;
  border-color: $accent-green;
  box-shadow: none !important;
}

.form-check-inline {
  @extend .mr-20, .align-center, .cursor-pointer;
  padding: 8px 12px;

  input {
    @extend .mr-4;

    &.form-check-input {
      @extend .ml-0, .position-relative, .cursor-pointer;

      &:checked {
        background-color: $white;
        border: 1px solid $green-50;
        background-image: none !important;

        &:after {
          position: absolute;
          display: block;
          content: "";
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: $accent-green;
          border: 1px solid $white;
          text-align: center;
          right: 1px;
          bottom: 1px;
        }
      }

      &:focus {
        box-shadow: none !important;
      }
    }

    &.radio-check-input {
      border: 2px solid $white;
      box-shadow: 0 0 0 1px $slate-60;
      appearance: none;
      border-radius: 50%;
      width: 12px;
      height: 12px;
      background-color: $white;
      transition: all ease-in 0.2s;
      @extend .cursor-pointer;

      &.ng-valid {
        border: unset !important;
      }

      &:checked {
        background-color: $accent-green;
        box-shadow: 0 0 0 1px $green-50;
        border: 2px solid $white !important;
      }

      &:disabled {
        opacity: 0.5;
      }
    }
  }

  label {
    @extend .cursor-pointer;
    white-space: nowrap;
    margin-top: 1px;
  }
}

.form-check {
  @extend .mb-0;
}

.btn-text-white {
  color: $white;

  .text-secondary {
    color: unset !important;
  }
}

//Google Button for Gmail Integration
.g-sign-in-button {
  margin: 10px;
  display: inline-block;
  width: 240px;
  height: 50px;
  background-color: $blue-70;
  color: $white;
  border-radius: 1px;
  box-shadow: 0 2px 4px 0 $black-40;
  transition: background-color 0.218s, border-color 0.218s, box-shadow 0.218s;
}

.g-sign-in-button:hover {
  cursor: pointer;
  -webkit-box-shadow: 0 0 3px 3px #4285f44d;
  box-shadow: 0 0 3px 3px #4285f44d;
}

.g-sign-in-button:active {
  background-color: $blue-60;
  transition: background-color 0.2s;
}

.g-sign-in-button .content-wrapper {
  height: 100%;
  width: 100%;
  border: 1px solid transparent;
}

.g-sign-in-button img {
  width: 18px;
  height: 18px;
}

.g-sign-in-button .logo-wrapper {
  padding: 15px;
  background: $white;
  width: 48px;
  height: 100%;
  border-radius: 1px;
  display: inline-block;
}

.g-sign-in-button .text-container {
  font-family: Roboto, arial, sans-serif;
  font-weight: 500;
  letter-spacing: 0.21px;
  font-size: 16px;
  line-height: 48px;
  vertical-align: top;
  border: none;
  display: inline-block;
  text-align: center;
  width: 180px;
}

.btn-gray {
  @extend .btn, .btn-sm, .br-5, .fw-semi-bold, .flex-center;
  background-color: $dark-400;
  color: $dark-700;
  min-width: 100px;

  &:hover,
  &:focus {
    background-color: $dark-400 !important;
    color: $dark-700 !important;
  }
}

.btn-coal {
  @extend .btn,
  .btn-sm,
  .bg-coal,
  .text-white,
  .br-5,
  .fw-semi-bold,
  .flex-center,
  .text-nowrap;
  min-width: 100px;
  padding: 0px 12px !important;

  &:hover,
  &:focus {
    background-color: $primary-black !important;
  }
}

.btn-green {
  @extend .btn,
  .btn-sm,
  .text-white,
  .w-90,
  .br-5,
  .fw-600,
  .flex-center,
  .text-large;

  background: linear-gradient(180deg,
      $accent-green-100 0%,
      $accent-green-200 76.04%);
  min-width: 100px;
}

.btn-bulk {
  @extend .btn,
  .btn-sm,
  .text-white,
  .text-xs,
  .fw-semi-bold,
  .mr-12,
  .px-16,
  .text-nowrap;

  background: linear-gradient(180deg,
      $accent-green-100 0%,
      $accent-green-200 76.04%);
  // padding: 0px 16px !important;

  &:hover,
  &:focus {
    color: linear-gradient(93.48deg, $red-80 15.96%, $red-90 96.63%) !important;
  }
}

.btn-bulk-1 {
  @extend .btn,
  .btn-sm,
  .text-white,
  .text-xs,
  .fw-semi-bold,
  .mr-12,
  .px-16,
  .text-nowrap,
  .bg-black-100;

  &:hover,
  &:focus {
    background-color: $black-70 !important;
  }
}

.btn-bulk-red {
  @extend .btn-bulk;
  background: linear-gradient(93.48deg, $red-80 15.96%, $red-90 96.63%);

  &:hover,
  &:focus {
    background: linear-gradient(93.48deg, $red-80 15.96%, $red-90 96.63%) !important;
  }
}

.btn-bulk-blue {
  @extend .btn-bulk;
  background: $blue-450;

  &:hover,
  &:focus {
    background-color: $blue-450 !important;
  }
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.disabled {
  pointer-events: none;
  opacity: 0.4;
}